# 填充材料

[TOC]

## 教育背景

- 南京大学 智能软件与工程专业 
  - gpa 4.3/5.0 前40%
  - 主修 数据结构与算法，机器学习，计算机操作系统，自然语言处理，C语言程序设计基础，模式识别与计算机视觉，高级算法，互联网计算等
  - 其它 微积分 线性代数 离散数学等

## 项目经验

- 作为团队负责人参与大学生创新创业项目《基于大数据和人工智能的超声诊断系统的开发与应用》，拥有两项专利“一种能够自动记录并指导扫查的超声诊断方法和系统”， “超声探头、超声检查系统、方法、电子设备及存储介质”
- 进入南京大学计算机学院"推理与学习"研究组，参与论文创新，主要涉猎少样本学习，大模型攻击与鲁棒性研究，模型跨域问题等。其中已完成的工作有参与github项目[LibFewShot](https://github.com/RL-VIG/LibFewShot)项目的开发（[LibFewShot: A Comprehensive Library for Few-shot Learning](https://www.computer.org/csdl/journal/tp/5555/01/10239698/1Qck3o85t60). Wenbin Li, Ziyi Wang, Xuesong Yang, Chuanqi Dong, Pinzhuo Tian, Tiexin Qin, Jing Huo, Yinghuan Shi, Lei Wang, Yang Gao, Jiebo Luo. In TPAMI 2023.）。其中个人参与的是项目中的少样本学习方法[MeTAL (ICCV 2021)](https://arxiv.org/abs/2110.03909)的复现重构工作
- 主导开发 \textbf{**基于FastAPI与LLM的南京大学手册RAG平台**}，基于\textbf{**SpringBoot**}开发服务端，结合分布式的Docker实现服务容器化，并利用校园云构建CI/CD流水线完成持续集成与自动化上线。同时结合向量数据库与大语言模型构建检索增强生成流程，实现对校内信息与校规通知的精准检索与回答，支持学生特殊情况和复杂案例的语义检索与回答。
- 单独一人开发项目[LLM](https://github.com/Aquila-zhou1/LLM)，完成了从预训练、全量监督微调、LoRA 微调，到 RLHF以及推理模型训练的一整套 LLM 训练流程
- 参与开发基于app[Know-Ur-Weather](https://github.com/KevinXu11155/Know-Ur-Weather)，一款鸿蒙操作系统的天气应用程序。
- 主导科研工作**[DDCPS](https://github.com/ZhizhengFu/DDCPS)**，一款Replacing ELO with Drift Diffusion Model (DDM) for Assessing Chess Player Skill Levels: A New Approach to Performance Evaluation的科研创新工作。目前正在撰写论文中，目标投稿CIG（CCF-B级会议）
- 可选：编译原理项目，操作系统项目，C++的2D游戏项目，ai音乐项目（对so-vits-*svc*的ai嗓音项目的尝试）等



## 技能特长

- 语言能力：托福，gre

- 计算机能力：C/C++，python，java, julia, matlab, mathematics
  - 工具框架 springboot, fastapi, pytorch, transformer, linux, git, docker
- 团队能力：

## 荣誉证书

2024年、2025年美国大学生数学建模竞赛S奖

2024学年人民奖学金文体活动奖

## 职业目标

同



## 补充材料：

- LLM技术文档：训练效率优化：**DeepSpeed ZeRO-2 加速训练**：利用 ZeRO-2 优化器，显著提升训练效率并优化显存资源利用。**FP16 混合精度 + CPU Offload**：引入混合精度训练与部分参数转移至 CPU，有效减少显存压力，加快训练速度。**4卡并行训练，内存占用减半**：多卡训练架构显著提升了训练吞吐量，同时降低了单卡显存负担。**Wandb 实时监控**：使用 Weights & Biases 实现训练过程可视化，便于观察模型收敛情况和调参，无需依赖手动 `print` 与日志检查。训练效果优化：**预先全监督微调增强 LoRA 效果**：在 LoRA 微调前进行一次全监督微调，有效提升最终微调性能。引入推理模型训练**：主动探索 LLM 新范式，通过训练推理模型进一步提升模型生成能力。**DPO 替代 PPO 优化 RLHF 流程**：DPO（Direct Preference Optimization）通过显式优化目标替代了原 PPO 的复杂奖励估计过程；仅需训练 Actor 与 Ref 模型，无需在线奖励模型，大幅降低计算与显存开销；同时提高了训练稳定性，且性能几乎无损。

- Know-Ur-Weather技术文档  （1） API来源  1. 天气数据供应商：高德地图开放平台  本应用所使用的API来自高德地图开放平台，其提供了丰富的地理信息和天气相关服务  2. API 接入方法  1) 获取城市编码接口：https://restapi.amap.com/v3/config/district  功能：该接口用于根据城市名称获取对应的城市编码  请求参数  keywords：经过encodeURIComponent 编码的城市名称，用于指定要查询的城市  subdistrict：设置为 0，表示不查询下级行政区  key：即API_KEY，用于身份验证，确保只有合法的开发者能够使用该 API  2) 获取实时天气接口：https://restapi.amap.com/v3/weather/weatherInfo  功能：该接口用于获取指定城市编码对应的实时天气数据  请求参数  city：城市编码，通过getCityId 方法获取，用于指定要查询天气的城市  key：同样为API_KEY，用于身份验证  （2） API使用，数据解析  1. 获取城市编码的API使用  1) 构建URL  用encodeURIComponent 对 cityName 编码，再构建URL  https://restapi.amap.com/v3/config/district?keywords=${encodedCity}&subdistrict=0&key= ${API_KEY}，  其中keywords是编码后的城市名，subdistrict=0不查下级行政区，key为认证密钥  2) 发送请求  用http.createHttp()创建 HttpRequest 对象，通过 httpRequest.request 发 GET 请求  3) 处理响应  请求完成后，用await获取HttpResponse对象，打印状态码。若状态码为OK，将响应转字 符串，用于后续解析  2. 获取实时天气的API使用  1) 构建URL  getNowWeather 方法中，构建URL  ${BASE_URL}/weatherInfo?city=${cityCode}&key=${API_KEY}，  BASE_URL 为天气 API 基础地址，city是城市编码，key为密钥  2) 发送与处理  与获取城市编码类似，发GET请求、设置相同请求头。获响应后打印状态码和数据，若成功 则将响应转字符串用于解析  3. 数据解析  1) 获取城市编码  请求成功后，用JSON.parse将响应字符串解析为DistrictResponse对象  判断status 为1且districts 有数据，提取首个城市的adcode返回  2) 获取实时天气  请求成功后，解析响应为GaoDeWeatherResponse对象  判断status 为1且lives有数据，从中提取温度、天气状况、湿度信息，赋值给WeatherData  （3） 数据库的实现  1. 数据库初始化  1) 数据库创建  使用SQL语句定义数据库的表结构，包括存储天气数据各个字段及它们的类型  2) 数据库初始化  通过initDatabase 方法进行实际的数据库创建操作  包括设置数据库配置（名称和安全级别），获取 RdbStore 对象，以及创建 weather_data 表  2. 数据操作方法  1) 保存天气数据  创建ValuesBucket 对象 value，将 WeatherData 对象中的数据以及城市名称和当前时间填入  用insert 方法将ValuesBucket 中的数据插入weather_data表中  2) 获取最新天气数据  getLatestWeatherData 方法用于获取指定城市的最新天气数据  3) 获取天气历史记录  getWeatherHistory 方法用于获取指定城市的历史记录，通过传入limit参数获取对应数量  4) 清楚天气历史数据  clearWeatherHistory 方法用于清除指定城市的所有天气历史数据  （4） UI布局实现：导航和页面的响应式布局  1. 整体布局  1) 整体：使用 Column 组件作为根组件，构建了一个垂直方向的布局结构  2) 包含：在这个 Column 中包含了多个子组件，形成了应用的整体页面布局  3) 分区：整个布局分为三个主要部分：顶部导航栏、Tabs 组件和页面内容。  2. 导航栏布局  1) 作用：展示APP中最重要的内容——当前城市信息  2) 布局：使用 Row 组件实现水平布局，包含：  一个显示当前城市的Text组件、一个Blank占位组件、一个用于刷新的Button组件  3. Tabs组件  1) 作用：实现标签页切换功能，将页面划分为多个不同的部分  2) 布局：包括当前天气、天气详情和设置三个页面  4. 响应式布局  1) 当前天气页面：使用Column组件构建垂直布局，根据isLoading状态显示不同的内容  当isLoading 为true 时，显示LoadingProgress 组件，提示用户正在加载数据；  当isLoading 为false 时，显示WeatherCard 组件和更新时间信息。  2) 天气详情页面  用Stack 组件包裹Web组件，使用src属性设置网页的源地址，通过controller进行控制  3) 设置页面  使用Column包含List，List中包含多个ListItem，以列表形式展示设置选项  （5） 突出体现我们的离线缓存与数据同步  1. 存储机制：偏好设置存储（Preferences）  定义存储的名称为 PREFERENCES_NAME，其值为 'weather_preferences'，作为存储数据的容器  2. 存储和同步方法  1) 存储和获取当前城市  a. 存储：saveCity(city: string): Promise  将用户当前选择的城市存储到偏好设置中，以便以后能够记住用户选择的城市  b. 获取：getCurrentCity(): Promise  从偏好设置中获取之前存储的用户当前城市信息  2) 存储和获取更新时间  a. 存储：saveUpdateTime(time: string): Promise  存储最近一次天气数据更新的时间，方便后续检查数据的时效性  b. 获取：getLastUpdateTime(): Promise  从存储偏好中获取最近一次天气数据更新的时间  3) 存储和获取天气数据  a. 存储：saveWeatherData(weatherData: WeatherData): Promise  将WeatherData 类型的天气数据存储到偏好设置中，需要转化为JSON字符串存储  b. 获取：getCachedWeatherData(): Promise  从偏好设置中获取存储的天气数据，并将其JSON字符串解析为WeatherData对象  4) 存储和获取城市列表  a. 存储：saveCityList(cityList: string[]): Promise  存储用户关注的城市列表，以数组形式存储，需要将数组序列化为 JSON 字符串  b. 获取：getCityList(): Promise  从存储偏好中获取存储的城市列表，并将存储的 JSON 字符串解析为字符串数组  （6） 多线程优化的实现  1. 模块依赖：@ohos.taskpool  提供了创建和管理并发任务的功能，使得getUrl 函数能够在独立的线程中执行，避免了主线 程的阻塞，提高了程序的并发处理能力和响应速度。  2. URLTask 类  1) 功能：提供了一个静态方法getUrlForWebView，用于创建并执行getUrl任务，并对任务执 行结果进行处理  2) 方法：getUrlForWebView  参数：currentCity：表示当前城市的名称，类型为字符串，例如 "上海"  CityID：表示城市的唯一标识，类型为字符串，例如 "310000"  CurBp：表示屏幕断点类型，类型为字符串，例如 "sm" 或其他  3) 实现细节  创建 taskpool.Task 对象，将 getUrl 函数和所需参数传递给它  使用 await taskpool.execute(task) 执行任务，并将结果存储在 res 变量中  若执行成功，将结果作为 URL 输出；若出现异常，打印错误信息并返回 -1  3. 使用方法  1) 首先，确保已经正确导入 UrlTask 类和相关依赖  2) 调用UrlTask.getUrlForWebView(currentCity, CityID, CurBp)方法，将所需参数传递进去  3) 等待getUrlForWebView 方法返回生成的URL  4) 使用返回的 URL 执行后续操作，如使用this.controller.loadUrl(this.url)加载相应的网页内容

- DDCPS：Traditional chess rating systems, such as Elo, have limitations in accurately assessing player skill levels. They struggle to capture individual move quality and require numerous games to stabilize ratings. To address these issues, we introduce DDCPS (Drift Diffusion Chess Player Skill Evaluation), a novel approach inspired by the Drift Diffusion Model (DDM). DDCPS evaluates player skill based on move quality, offering a more dynamic and precise assessment.

  - 🛠️ Features：

    - Move-by-move analysis: Uses centipawn loss (CPL) changes to track real-time performance.

    - Fast skill evaluation: Determines player ability in significantly fewer games than Elo.

    - Convergence properties: Retains Elo’s stability while improving early-stage rating accuracy.

  - 📊 Methodology

    - Drift Rate Calculation: Determines player skill in real-time using a customized activation function.

    - Boundary Setting: Defines decision boundaries for skill differentiation.

    - Discrete Time Representation: Tracks each move as a discrete step to model game progress.

  - 🔬 Experiments & Results

    - Player Skill Evaluation: DDCPS successfully distinguishes player skill gaps within 20-30 games.

    - Convergence Property: The model stabilizes player ratings over time, similar to Elo.

