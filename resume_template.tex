%-------------------------
% Resume in Latex
% Author : So<PERSON><PERSON><PERSON>j
% License : MIT
%------------------------

\documentclass[letterpaper,11pt]{article}
\usepackage{multicol}
\usepackage{url}
\usepackage{latexsym}
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage[usenames,dvipsnames]{color}
\usepackage{verbatim}
\usepackage{enumitem}
\usepackage[hidelinks]{hyperref}
\usepackage{fancyhdr}
\usepackage[english]{babel}
\usepackage{tabularx}
\usepackage[UTF8]{ctex}
\usepackage{amsmath}
% \usepackage{fontspec}

% \usepackage{xeCJK}
% \setCJKmainfont{SimSun} % 可以替换为你需要的字体

\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Adjust margins
\addtolength{\oddsidemargin}{-0.5in}
\addtolength{\evensidemargin}{-0.5in}
\addtolength{\textwidth}{1in}
\addtolength{\topmargin}{-.5in}
\addtolength{\textheight}{1.0in}

\urlstyle{same}

\raggedbottom
\raggedright
\setlength{\tabcolsep}{0in}

% Sections formatting
\titleformat{\section}{
  \vspace{-4pt}\scshape\raggedright\large
}{}{0em}{}[\color{black}\titlerule \vspace{-5pt}]

%-------------------------
% Custom commands
\newcommand{\resumeItem}[2]{
  \item\small{
    \textbf{#1}{: #2 \vspace{-2pt}}
  }
}

\newcommand{\resumeSubheading}[4]{
  \vspace{-1pt}\item
    \begin{tabular*}{0.97\textwidth}[t]{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\
      \textit{\small#3} & \textit{\small #4} \\
    \end{tabular*}\vspace{-5pt}
}

\newcommand{\resumeSubSubheading}[2]{
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \textit{\small#1} & \textit{\small #2} \\
    \end{tabular*}\vspace{-5pt}
}

\newcommand{\resumeSubItem}[2]{\resumeItem{#1}{#2}\vspace{-4pt}}

\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=*]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}
\newcommand{\resumePoint}[2]{{\textbf{#1}{: #2\vspace{-2pt}}\newline}}
\newcommand{\RomanNumeralCaps}[1]{\text{%
    \uppercase\expandafter{\romannumeral #1}}}
% \newcommand{\MyFont}[1]{{\fontspec{Microsoft YaHei} #1}} % 自定义命令

%-------------------------------------------
%%%%%%  CV STARTS HERE  %%%%%%%%%%%%%%%%%%%%%%%%%%%%


\begin{document}

%----------HEADING-----------------
\begin{tabular*}{\textwidth}{l@{\extracolsep{\fill}}r}
  \href{}{\Huge \CJKfontspec{SimHei} 程芃幻} & 邮箱：\href{mailto:<EMAIL>}{<EMAIL>}\\ \large Penghuan Cheng & 手机号 : 13679201707 \\
\end{tabular*}

%-----------EDUCATION-----------------
%-----------EDUCATION-----------------
\section{教育背景}
  \resumeSubHeadingListStart
  \resumeSubheading
    {南京大学}{南京, 中国}
    {软件工程本科（预计2026年毕业）}{2022.09 -- 至今}
    \resumeSubItem{专业成绩}{GPA 4.43/5.0，排名前30\%，具备扎实的计算机基础与数学能力}
    \resumeSubItem{主修课程}{数据结构与算法、编译原理、计算机操作系统、机器学习、C语言程序设计基础、计算系统基础、计算机组成结构、C++高级程序设计、软件工程与计算、人机交互系统、互联网计算等}
    \resumeSubItem{其它课程}{微积分、线性代数、离散数学、大学英语等}
  \resumeSubHeadingListEnd
%-----------EXPERIENCE-----------------
\section{项目经验}
\resumeSubHeadingListStart
    \item 协助师兄完成论文《
Edge-end Heterogeneous Collaborative Learning by Prototype Selection and Edge Association》，投稿至\textbf{TMC 2025}，负责边端协作训练方法在Jetson Nano和树莓派的实机实验部署。
    \item 参与\textbf{国家级大学生创新项目《自演进边端合作学习方法》}，负责边缘多机合作学习方法的实现。
    \item 参与张邵群老师指导项目《基于大数据和人工智能的超声诊断系统的开发与应用》，负责利用图像处理技术提取超声图像的关键特征，同时进行统计分析。
    \item 主导开发与部署\textbf{DevOps驱动的音乐播放平台部署}，基于\textbf{SpringBoot}开发服务端，结合Docker实现服务容器化，并利用校园云构建CI/CD流水线完成持续集成与自动化上线。
    \item 开发\textbf{基于FastAPI的法律问答RAG系统}，结合向量数据库与大语言模型构建检索增强生成流程，实现对法律条款与案例的精准检索与回答，支持复杂案例的语义检索与问答，显著提升法律问答的准确性与可解释性


\resumeSubHeadingListEnd
      
%-----------PROJECTS-----------------
\section{技能特长}
  \resumeSubHeadingListStart
    \resumeSubItem{语言能力}
      {大学英语CET-4获得564分，CET-6获得509分，能够熟练地用英文进行交流和读写。}
    \resumeSubItem{计算机能力}
      {熟练掌握C/C++、java、python等编程语言，CCF CSP认证300分（全国前6\%）。}
      \resumeSubItem{工具框架}{SpringBoot、FastAPI、PyTorch、Linux、Git、Docker}
    \resumeSubItem{团队能力}
    {具有良好的沟通能力和奉献精神，多次组队参加比赛、项目等。}
 
  \resumeSubHeadingListEnd


%--------PROGRAMMING SKILLS------------
\section{荣誉证书}
  \resumeSubHeadingListStart
    \resumeSubItem{比赛项目}
      {2024年C4网络技术挑战赛华东赛区二等奖，2024年、2025年美国大学生数学建模竞赛S奖, 2024年全国大学生数学建模竞赛赛区一等奖}
    \resumeSubItem{奖学金}
      {2023学年人民奖学金一等奖，2024学年人民奖学金三等奖}
  \resumeSubHeadingListEnd
%-------------------------------------------

\section{职业目标}
希望加入贵公司从事\textbf{人工智能、数据智能或后端与分布式系统相关研发岗位}，发挥在深度学习、图像处理与边端协作方面的研究积累，同时运用对SpringBoot、FastAPI等框架及DevOps工具链的掌握，解决实际业务中的智能计算与后端系统问题。具备扎实的计算机基础与较强的工程实现能力，能够独立完成从算法设计到系统部署的完整开发流程。在工作中希望不断提升大规模分布式训练、高效模型部署与后端系统架构设计的能力，进一步拓展在机器学习、计算机视觉、多模态融合及高并发服务开发等方向的专业技能。同时，期待在团队协作与企业项目中积累更多工程经验，成长为一名兼具研究能力与工程能力的复合型研发人才。
\end{document}